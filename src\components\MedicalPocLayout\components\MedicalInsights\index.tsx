import "react-pdf/dist/Page/AnnotationLayer.css";
import "react-pdf/dist/Page/TextLayer.css";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { AnimatePresence, motion } from "framer-motion";
import { pdfjs, Document, Page } from "react-pdf";
import { Raleway } from "next/font/google";
import { SamplesGrid } from "..";
import * as Yup from "yup";
import { sections, tabs } from "@constants/tabs";
import cx from "classnames";
import { pdfType } from "@utils";
import { EMPTY_PROJECT_NAME, MAX_PROJECTS } from "@constants";
import { SampleState } from "@typings";
import Insights from "./Insights";
import QA from "./QA";
import { QAResponse, QueryState } from "./QA/typings";
import { MedicalInsightsProps } from "./typings";
import Modal from "@components/Modal";
import { FormikValues, useFormik } from "formik";
import Loader from "@components/Loader";
import { statuses } from "@constants/db";
import MedicalResult from "./MedicalResult";
import { Upload } from "@public/assets/icons";

const raleway = Raleway({
  display: "swap",
  weight: ["400", "500", "600"],
  subsets: ["latin"],
});

const MedicalInsights: React.FC<MedicalInsightsProps> = ({
  data,
  sampleIndex,
  medicalDetails,
  handleSample,
  handleMultipleFiles,
  handleMultipleDrop,
  activeSection,
  handleChangeSection,
  handleQAModal,
  handleQAError,
  resetSample,
  handleUploadLoader,
  projectStatus,
  projects,
  selectedProject,
  handleSelectedProject,
  setTheSelectedProject,
  handleProjectSubmission,
  projectLoading,
  sampleResult,
  handleSampleResult,
  getSampleDetails,
  handleDownloadSample,
}) => {
  pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;
  const [projectModalOpen, setProjectModalOpen] = useState<boolean>(false);
  const [tab, setTab] = useState<string>(tabs.insights);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const fileRef = useRef<HTMLInputElement>(null);
  const [counter, setCounter] = useState<number>(0);
  const [resultQueries, setResultQueries] = useState<QueryState[]>([]);
  const [queryResult, setQueryResult] = useState<QAResponse>({
    query: "",
    result: "",
  });
  const dragTimeout = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [queryLoader, setQueryLoader] = useState<boolean>(false);
  const [pdfOpen, setPdfOpen] = useState<boolean>(false);
  const [currentPDF, setCurrentPDF] = useState<{
    pageNo: number;
    documentName: string;
  }>({
    pageNo: 1,
    documentName: "",
  });
  const [currentPDFPage, setCurrentPDFPage] = useState<number>(
    currentPDF.pageNo
  );
  const [currentPDFTotalPages, setCurrentPDFTotalPages] = useState<number>(1);
  const [pdfPageData, setPDFPageData] = useState<string | null>(null);

  const handleDocumentLoading = ({ numPages }: any) => {
    setCurrentPDFTotalPages(numPages);
  };

  const handleDragEnter = (e: React.DragEvent<HTMLLabelElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    (dragTimeout.current as any) = setTimeout(() => {
      setIsDragging(false);
    }, 200);
  };

  const handleSectionTab = (selectedTab: string) => {
    handleChangeSection(selectedTab);
  };

  const isInsightsActive = tab === tabs.insights;
  const isQAActive = tab === tabs.qa;

  const selectPdf = useCallback(async (sample: SampleState, index: number) => {
    const { path } = sample;
    handleSample(path, index);
    const absolutePath = sample?.jsonPath!;
    const currentSample = { ...sample, index };
    if (window && typeof window !== undefined) {
      window.localStorage.setItem(
        "currentSample",
        JSON.stringify(currentSample)
      );
      const isCurrentProjectExists = JSON.parse(
        window.localStorage.getItem("currentProject")!
      );
      if (
        isCurrentProjectExists &&
        isCurrentProjectExists !== null &&
        isCurrentProjectExists?.project_id !== -1
      )
        window.localStorage.removeItem("currentProject");
    }
    await getSampleDetails(absolutePath);
  }, []);

  const handleDragOver = useCallback(
    (event: React.DragEvent<HTMLDivElement | HTMLLabelElement>) => {
      event.preventDefault();
      clearTimeout(dragTimeout.current as any);
    },
    []
  );

  useEffect(() => {
    if (window && typeof window !== undefined) {
      const count = Number(window.localStorage.getItem("totalQueries"));
      const queries = JSON.parse(window.localStorage.getItem("queries") as any);
      const loader = JSON.parse(
        window.localStorage.getItem("isFetching") as any
      );

      console.log("count, queries, loader", { count, queries, loader });
      setCounter(count);
      setQueryLoader(loader);
      setResultQueries(queries);
    }
  }, []);

  useEffect(() => {
    if (window && typeof window !== undefined) {
      window.localStorage.setItem("queries", JSON.stringify(resultQueries));
    }
    if (containerRef && containerRef.current) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight;
    }
  }, [resultQueries]);

  useEffect(() => {
    if (window && typeof window !== undefined) {
      window.localStorage.setItem("totalQueries", JSON.stringify(counter));
    }
  }, [counter]);

  useEffect(() => {
    if (window && typeof window !== undefined) {
      window.localStorage.setItem("isFetching", JSON.stringify(queryLoader));
    }
  }, [queryLoader]);

  useEffect(() => {
    if (window && typeof window !== undefined) {
      window.localStorage.setItem("queries", JSON.stringify([]));
      window.localStorage.setItem("totalQueries", JSON.stringify(0));
      window.localStorage.setItem("isFetching", JSON.stringify(false));
      setResultQueries([]);
      setCounter(0);
      setQueryLoader(false);
    }
  }, [sampleIndex, selectedProject?.project_id]);

  const resetCounter = () => {
    setCounter(0);
  };
  const handleCounter = () => {
    setCounter(counter + 1);
  };
  const handleQueryLoader = (status: boolean) => {
    setQueryLoader(status);
  };

  useEffect(() => {
    setResultQueries((prevQueries: QueryState[]) => {
      const updatedQueries = prevQueries ? [...prevQueries] : resultQueries;
      const matchedQueries = updatedQueries?.filter(
        (prevQuery: QueryState) => prevQuery?.query === queryResult?.query
      );
      const multiple = matchedQueries?.length > 1;
      if (matchedQueries?.length > 0) {
        if (multiple) {
          const sortedQueries = matchedQueries?.sort(
            (queryOne: QueryState, queryTwo: QueryState) =>
              queryTwo?.id - queryOne?.id
          );
          const latestQueryId = sortedQueries[0]?.id;
          const latestQueryIndex = updatedQueries?.findIndex(
            (existingQuery: QueryState) => existingQuery?.id === latestQueryId
          );
          updatedQueries[latestQueryIndex].result = queryResult?.result;
        } else {
          const matchedQueryIndex = updatedQueries?.findIndex(
            (existingQuery: QueryState) =>
              existingQuery?.query === queryResult?.query
          );
          updatedQueries[matchedQueryIndex].result = queryResult?.result;
        }
      }
      return updatedQueries;
    });
    if (window && typeof window !== undefined) {
      window.localStorage.setItem("queries", JSON.stringify(resultQueries));
    }
  }, [queryResult]);

  useEffect(() => {
    setTab(tabs.insights);
    handleChangeSection(sections.summary);
  }, [sampleIndex, selectedProject?.project_id]);

  const {
    values,
    errors,
    setErrors,
    setValues,
    handleSubmit,
    handleChange,
    resetForm,
  } = useFormik({
    initialValues: {
      projectName: "",
    },
    validationSchema: Yup.object().shape({
      projectName: Yup.string().required(EMPTY_PROJECT_NAME),
    }),

    onSubmit: (values: FormikValues) => {
      handleProjectSubmission(values)
        .then(() => resetForm())
        .then(() => setProjectModalOpen(false));
    },
  });
  const projectNameRef = useRef<HTMLInputElement>(null);
  useEffect(() => {
    setErrors({});
    if (!projectModalOpen) {
      setValues({});
    }
    if (projectNameRef && projectNameRef.current) {
      projectNameRef.current.focus();
    }
  }, [projectModalOpen]);

  useEffect(() => {
    if (selectedProject?.project_id !== -1) {
      resetSample();
      handleSampleResult(false);
    }
  }, [selectedProject?.project_id]);

  useEffect(() => {
    if (sampleIndex > -1) {
      const project = {
        project_id: -1,
        project_name: "",
      };
      setTheSelectedProject(project);
    }
  }, [sampleIndex]);

  const renderInsights = () => {
    if (sampleResult) {
      return (
        <MedicalResult
          sampleName={data?.samples?.[sampleIndex]?.fileName}
          projectName={selectedProject?.project_name}
          response={medicalDetails}
          handleTab={setTab}
          medicalDetails={medicalDetails}
          isInsightsActive={isInsightsActive}
          isQAActive={isQAActive}
          insights={
            <Insights
              isSample={sampleIndex > -1}
              sampleNo={sampleIndex + 1}
              activeSection={activeSection}
              medicalDetails={medicalDetails}
              handleSectionTab={handleSectionTab}
              project={selectedProject}
              handlePDFModalOpen={setPdfOpen}
              handleCurrentPDF={setCurrentPDF}
              handleCurrentPDFPage={setCurrentPDFPage}
              handlePDFPageData={setPDFPageData}
            />
          }
          qa={
            <QA
              isSample={sampleIndex > -1}
              sample={data?.samples?.[sampleIndex]}
              project={selectedProject}
              containerRef={containerRef}
              resultQueries={resultQueries}
              handleResultQueries={setResultQueries}
              addQueryResult={setQueryResult}
              counter={counter}
              queryLoader={queryLoader}
              handleQueryLoader={handleQueryLoader}
              handleCounter={handleCounter}
              handleQAModal={handleQAModal}
              handleQAError={handleQAError}
              resetCounter={resetCounter}
            />
          }
        />
      );
    } else {
      if (projects?.length === 0 || selectedProject?.project_id === -1) {
        return (
          <AnimatePresence>
            <motion.div
              className="flex flex-col gap-y-4 items-center justify-center h-96 rounded-lg bg-brand-primary/10 p-8"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <i className="fas fa-info-circle text-4xl lg:text-5xl text-brand-primary/70 group-hover:scale-95 transition duration-200" />
              <span className="text-xl md:text-lg lg:text-2xl text-brand-primary/70 font-semibold text-center max-w-xl">
                {projects.length !== 0 && selectedProject?.project_id === -1
                  ? "Select any one project or sample to show the details"
                  : "You don't have any existing projects, create a new project or select any sample"}
              </span>
            </motion.div>
          </AnimatePresence>
        );
      } else if (selectedProject?.project_id !== -1) {
        if (projectStatus === statuses.Created) {
          return (
            <motion.label
              id="uploadLabel"
              htmlFor="file"
              className={cx(
                "flex items-center justify-center rounded-xl w-full h-96 p-4 hover:bg-brand-primary/10 transition duration-200 min-h-[400px]",
                {
                  "bg-brand-primary/10 bshadow-primary-light": isDragging,
                  "bshadow-primary-light": !isDragging,
                }
              )}
              onDragEnter={handleDragEnter}
              onDragLeave={handleDragLeave}
              onDragOver={handleDragOver}
              onDrop={(e) => handleMultipleDrop(e, selectedProject)}
              whileHover={{ cursor: "pointer" }}
            >
              <form encType="multipart/form-data">
                <input
                  ref={fileRef}
                  type="file"
                  id="file"
                  name="pdf"
                  multiple
                  className="hidden"
                  onChange={(e) => handleMultipleFiles(e, selectedProject)}
                  accept={pdfType}
                />
                <motion.span
                  className="flex flex-col space-y-4 items-center justify-center focus:outline-none"
                  initial={{ scale: 1 }}
                  transition={{ duration: 0.2 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <i className="fas fa-file-pdf text-5xl text-brand-primary/70"></i>
                  <span className="text-[26px] text-center text-grayish font-semibold text-brand-primary/70">
                    Drag & drop pdf files
                  </span>
                </motion.span>
              </form>
            </motion.label>
          );
        } else if (projectStatus === statuses.Processing) {
          return (
            <AnimatePresence>
              <motion.div
                className="flex flex-col gap-y-4 items-center justify-center h-96 rounded-lg bg-brand-warning/10 p-8"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                <i className="fas fa-clock text-4xl lg:text-5xl text-brand-warning/70 group-hover:scale-95 transition duration-200" />
                <span className="text-2xl md:text-lg lg:text-2xl text-brand-warning/70 font-semibold text-center max-w-xl">
                  Processing your document(s)
                </span>
              </motion.div>
            </AnimatePresence>
          );
        } else if (projectStatus === statuses.Failed) {
          return (
            <AnimatePresence>
              <motion.div
                className="flex flex-col gap-y-4 items-center justify-center h-96 rounded-lg bg-brand-secondary/10 p-8"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                <i className="fas fa-warning text-4xl lg:text-5xl text-brand-secondary/70 group-hover:scale-95 transition duration-200" />
                <span className="text-2xl md:text-lg lg:text-2xl text-brand-secondary/70 font-semibold text-center max-w-xl">
                  Something went wrong!
                </span>
              </motion.div>
            </AnimatePresence>
          );
        } else if (projectStatus === statuses.Completed) {
          return (
            <MedicalResult
              sampleName={data?.samples?.[sampleIndex]?.fileName}
              projectName={selectedProject?.project_name}
              response={medicalDetails}
              medicalDetails={medicalDetails}
              handleTab={setTab}
              isInsightsActive={isInsightsActive}
              isQAActive={isQAActive}
              insights={
                <Insights
                  isSample={sampleIndex > -1}
                  sampleNo={sampleIndex + 1}
                  activeSection={activeSection}
                  handleSectionTab={handleSectionTab}
                  medicalDetails={medicalDetails}
                  project={selectedProject}
                  handlePDFModalOpen={setPdfOpen}
                  handleCurrentPDF={setCurrentPDF}
                  handleCurrentPDFPage={setCurrentPDFPage}
                  handlePDFPageData={setPDFPageData}
                />
              }
              qa={
                <QA
                  isSample={sampleIndex > -1}
                  sample={data?.samples?.[sampleIndex]}
                  project={selectedProject}
                  containerRef={containerRef}
                  resultQueries={resultQueries}
                  handleResultQueries={setResultQueries}
                  addQueryResult={setQueryResult}
                  counter={counter}
                  queryLoader={queryLoader}
                  handleQueryLoader={handleQueryLoader}
                  handleCounter={handleCounter}
                  handleQAModal={handleQAModal}
                  handleQAError={handleQAError}
                  resetCounter={resetCounter}
                />
              }
            />
          );
        }
      }
    }
  };
  return (
    <>
      <motion.div
        className="flex flex-col space-y-20 mx-auto transform duration-200 lg:translate-y-32"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <div className="flex flex-col space-y-4 items-center text-brand-black px-16 pt-16">
          <h2 className="text-4xl font-semibold max-w-2xl text-center">
            Try Medical Documents Insights Extractor!
          </h2>
          <p
            className={`${raleway.className} max-w-lg font-medium text-center`}
          >
            Upload a Medical document to extract insights like Summary,
            Entities, PHI dates and Encounter dates.
          </p>
        </div>

        <motion.div
          className="relative flex flex-col w-full"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <div className="p-8">
            <div className="flex flex-wrap justify-center gap-12">
              <div>
                <div className="flex gap-x-4 relative text-4xl text-brand-secondary/60 font-bold h-12 w-fit mb-4">
                  <i className="fa fa-file-pdf"></i>
                  <span>Samples</span>
                </div>
                <SamplesGrid
                  handleDownloadSample={handleDownloadSample}
                  sampleIndex={sampleIndex}
                  samples={data?.samples}
                  selectFile={selectPdf}
                />
              </div>
              <div>
                <div className="flex gap-x-4 relative text-4xl text-brand-warning/60 font-bold h-12 w-fit mb-4">
                  <i className="fa fa-project-diagram"></i>
                  <span>Projects</span>
                </div>
                {projectLoading ? (
                  <Loader classNames="w-full h-[255px]" />
                ) : projects?.length === 0 ? (
                  <AnimatePresence>
                    <motion.div
                      className="group flex flex-col gap-y-4 items-center justify-center py-20 px-36 rounded-lg bg-brand-warning/10 transition duration-200 cursor-pointer hover:bg-brand-warning/20"
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0 }}
                      onClick={() => setProjectModalOpen(true)}
                    >
                      <i className="fa fa-plus-circle text-5xl text-brand-warning/70 group-hover:scale-95 transition duration-200" />
                      <span className="text-2xl md:text-lg lg:text-2xl text-brand-warning/70 font-semibold text-center">
                        Create new project
                      </span>
                    </motion.div>
                  </AnimatePresence>
                ) : (
                  <div
                    className={cx(
                      "flex gap-6 shadow-md flex-wrap justify-center p-6 rounded-lg",
                      {
                        "bg-brand-warning/5 shadow-brand-warning/30":
                          selectedProject?.project_id !== -1,
                      }
                    )}
                  >
                    {projects?.length < MAX_PROJECTS && (
                      <div
                        className={cx(
                          "group flex flex-col gap-y-4 items-center justify-center w-[136px] h-[206px] rounded-lg transition duration-200 cursor-pointer",
                          {
                            "bg-white shadow-md hover:bg-brand-warning/10":
                              selectedProject?.project_id !== -1,
                            "bg-brand-warning/10 hover:bg-brand-warning/20":
                              selectedProject?.project_id === -1,
                          }
                        )}
                        onClick={() => setProjectModalOpen(true)}
                      >
                        <i className="fa fa-plus-circle text-5xl text-brand-warning/70 group-hover:scale-95 transition duration-200" />
                        <div className="text-lg text-brand-warning/70 font-semibold text-center leading-tight">
                          Create new project
                        </div>
                      </div>
                    )}
                    <AnimatePresence mode="popLayout">
                      {projects?.map((project, index) => {
                        const { project_id, project_name } = project;
                        return (
                          <motion.div
                            layout
                            className={cx(
                              "relative flex flex-col rounded-lg w-[136px] h-[206px] p-4 font-semibold text-brand-warning/80 text-xl cursor-pointer transition duration-200 bg-white",
                              {
                                "bshadow-warning-light":
                                  selectedProject?.project_id ===
                                  project?.project_id,
                                bshadow:
                                  selectedProject?.project_id !==
                                  project?.project_id,
                                "pointer-events-none": projectLoading,
                              }
                            )}
                            key={project_id}
                            initial={{ opacity: 0, y: 0, scale: 0.8 }}
                            animate={{ opacity: 1, y: 0, scale: 1 }}
                            exit={{ opacity: 0, y: -10, scale: 0.8 }}
                            onClick={() =>
                              handleSelectedProject(
                                { project_id, project_name },
                                handleUploadLoader
                              )
                            }
                          >
                            {selectedProject?.project_id ===
                              project?.project_id && (
                              <span className="absolute -top-3 -right-1">
                                <i className="fas fa-circle-check text-brand-warning/60 text-base"></i>
                              </span>
                            )}
                            {selectedProject?.project_id ===
                              project?.project_id && (
                              <motion.label
                                htmlFor="exFile"
                                className="absolute -bottom-3 -right-1 z-10 cursor-pointer upload-icon"
                                whileHover={{
                                  scale: 0.9,
                                }}
                              >
                                <Upload />
                                <input
                                  ref={fileRef}
                                  type="file"
                                  id="exFile"
                                  name="pdf"
                                  multiple
                                  className="hidden"
                                  onChange={(e) => {
                                    e.stopPropagation();
                                    handleMultipleFiles(e, selectedProject);
                                  }}
                                  accept={pdfType}
                                />
                              </motion.label>
                            )}
                            {projectLoading && (
                              <div className="absolute h-full w-full bg-black bg-opacity-10 top-0 left-0 z-20 pointer-events-none cursor-pointer">
                                <Loader classNames="sm project translate-y-[90px]" />
                              </div>
                            )}
                            <span
                              className="translate-y-6 text-center line-clamp-2 max-h-12 leading-tight text-lg"
                              title={project_name}
                            >
                              {project_name}
                            </span>
                            <span
                              className={`${raleway.className} absolute top-0 left-0 bg-brand-warning/80 rounded-tl-lg rounded-br-lg px-2 text-[10px] before:absolute before:h-[20px] before:w-[22px] text-white before:bg-white before:blur-[6px] before:top-0 before:left-[21px] before:skew-x-[-25deg] before:opacity-0 latest-animation before:z-40 overflow-hidden leading-loose`}
                            >
                              Project
                            </span>
                          </motion.div>
                        );
                      })}
                    </AnimatePresence>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="flex-1 flex flex-col gap-y-12 p-8 mx-auto d-width">
            {renderInsights()}
          </div>
        </motion.div>
      </motion.div>
      <Modal
        open={projectModalOpen}
        setOpen={setProjectModalOpen}
        styles="flex-col gap-y-8"
      >
        <span className="text-3xl text-brand-warning font-bold px-8 w-full">
          Create new project
        </span>
        <form onSubmit={handleSubmit} className="flex flex-col gap-y-8">
          <div className="relative flex flex-col gap-y-2 px-8">
            <span className="text-brand-warning">Project name</span>
            <input
              ref={projectNameRef}
              id="projectName"
              name="projectName"
              type="text"
              value={values.projectName}
              onChange={(e) => {
                e.target.value = e.target.value.replace(/^\s(?:.*\s)?$/g, "");
                handleChange(e);
              }}
              placeholder="Enter project name"
              className={cx("border p-3 rounded-lg focus:outline-none", {
                "border-brand-secondary/70": errors.projectName,
                "border-brand-warning/70": !errors.projectName,
              })}
            />
            {errors.projectName && (
              <span className="absolute -bottom-7 left-8 text-brand-secondary/80 text-sm font-medium">
                {errors.projectName as any}
              </span>
            )}
          </div>
          <button
            type="submit"
            className={cx(
              "bg-brand-warning/10 rounded-lg w-[120px] h-[50px] self-end mx-8 text-brand-warning font-bold hover:bg-brand-warning/20 transition duration-200 focus:outline-none disabled:bg-gray-200 disabled:text-gray-400",
              {
                "hover:scale-[.98]": !projectLoading,
              }
            )}
            disabled={projectLoading}
          >
            {projectLoading ? <Loader classNames="sm project" /> : "Create"}
          </button>
        </form>
      </Modal>
      <Modal
        isPDF
        open={pdfOpen}
        setOpen={setPdfOpen}
        styles="pdf-dimensions overflow-auto c-scroll"
        pdfData={[currentPDFPage, setCurrentPDFPage, currentPDFTotalPages]}
      >
        <Document
          file={pdfPageData}
          noData={
            <div className="w-full flex justify-center items-center text-xl font-semibold text-brand-secondary py-96 px-52">
              <Loader classNames="w-full h-full" />
            </div>
          }
          error={
            <div className="relative flex gap-x-3 items-center justify-center top-[360px] bg-brand-secondary/10 p-4 rounded-xl">
              <i className="fas fa-warning text-2xl text-brand-secondary"></i>
              <span className="font-semibold text-brand-secondary text-xl">
                Failed to load document
              </span>
            </div>
          }
          loading={
            <div className="w-full flex justify-center items-center text-xl font-semibold py-96 px-52 text-brand-secondary">
              <Loader classNames="w-full h-full" />
            </div>
          }
          onLoadSuccess={handleDocumentLoading}
        >
          <Page
            pageNumber={currentPDFPage}
            noData={
              <div className="w-full flex justify-center items-center text-xl font-semibold py-96 px-52 text-brand-secondary">
                <Loader classNames="w-full h-full" />
              </div>
            }
            loading={
              <div className="w-full flex justify-center items-center text-xl py-96 px-52 font-semibold text-brand-secondary">
                <Loader classNames="w-full h-full" />
              </div>
            }
            error={
              <div className="relative flex gap-x-3 items-center justify-center top-[360px] left-[150px] bg-brand-secondary/10 p-4 rounded-xl">
                <i className="fas fa-warning text-2xl text-brand-secondary"></i>
                <span className="font-semibold text-brand-secondary text-xl">
                  Failed to load page
                </span>
              </div>
            }
          />
        </Document>
      </Modal>
    </>
  );
};

export default MedicalInsights;
